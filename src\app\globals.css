@import "tailwindcss";

:root {
  --color-primary: #3B82F6; /* Blue 500 */
  --color-secondary: #1D4ED8; /* Blue 700 */
  --color-accent: #10B981; /* Emerald 500 */
  --color-neutral-100: #F8FAFC; /* Slate 50 */
  --color-neutral-200: #E2E8F0; /* Slate 200 */
  --color-neutral-300: #CBD5E1; /* Slate 300 */
  --color-neutral-400: #94A3B8; /* Slate 400 */
  --color-neutral-500: #64748B; /* Slate 500 */
  --color-neutral-600: #475569; /* Slate 600 */
  --color-neutral-700: #334155; /* Slate 700 */
  --color-neutral-800: #1E293B; /* Slate 800 */
  --color-neutral-900: #0F172A; /* Slate 900 */

  --background: var(--color-neutral-100);
  --foreground: var(--color-neutral-900);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--color-neutral-900);
    --foreground: var(--color-neutral-100);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

/* Select Box Styling */
select {
  appearance: none; /* Remove default browser styling */
  background-color: var(--background);
  border: 1px solid var(--color-neutral-300);
  border-radius: 0.375rem; /* Tailwind's rounded-md */
  padding: 0.5rem 2.5rem 0.5rem 1rem; /* Adjust padding for custom arrow */
  font-size: 1rem;
  line-height: 1.5rem;
  color: var(--foreground);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='none' stroke='%236B7280' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1.5em 1.5em;
}

select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3); /* Blue focus ring */
}

select:disabled {
  background-color: var(--color-neutral-200);
  color: var(--color-neutral-500);
  cursor: not-allowed;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-neutral-800);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* Button enhancements */
button {
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Input field enhancements */
input, textarea {
  transition: all 0.2s ease-in-out;
}

input:focus, textarea:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Card shadows */
.card-shadow {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-shadow:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

