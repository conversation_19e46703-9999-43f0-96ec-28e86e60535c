import type { <PERSON>ada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"]
});

export const metadata: Metadata = {
  title: "First Step School - Management System",
  description: "School management system for First Step School, Saurabh Vihar, Jaitpur, Delhi. Manage attendance, fees, and student records.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${poppins.className} bg-gradient-to-br from-slate-900 to-indigo-900`}>
        {children}
      </body>
    </html>
  );
}
